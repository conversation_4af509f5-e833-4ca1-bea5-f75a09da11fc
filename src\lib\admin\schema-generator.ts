// "use server";

import { TableConfig } from "@/database/utils";
import { DbTable, DbColumn } from "@/types/db";
import { adminTableConfig } from "./config";
import { type EnabledTableKeys, type ColumnType, type SchemaInfo, type ColumnInfo } from "@/types/admin";
import * as schema from "@/database/schema";

const drizzleTypeToTsType: Record<string, ColumnType> = {
  // PostgreSQL types
  PgText: "text",
  PgVarchar: "string",
  PgChar: "string",
  PgInteger: "number",
  PgBigInt53: "number",
  PgBoolean: "boolean",
  PgTimestamp: "date",
  PgDate: "date",
  PgEnum: "enum",
  PgJsonb: "json",
  PgJson: "json",
  PgUUID: "uuid",
  PgSerial: "number",
  PgBigSerial53: "number",
  PgReal: "number",
  PgDoublePrecision: "number",
  PgNumeric: "number",
  PgTime: "string",
  PgInterval: "string",
  PgBytea: "file",
  
  // MySQL types
  MySqlText: "text",
  MySqlVarChar: "string",
  MySqlChar: "string",
  MySqlVarBinary: "file",
  MySqlBinary: "file",
  MySqlInt: "number",
  MySqlBigInt53: "number",
  MySqlTinyInt: "number",
  MySqlSmallInt: "number",
  MySqlMediumInt: "number",
  MySqlBoolean: "boolean",
  MySqlTimestamp: "date",
  MySqlDate: "date",
  MySqlDateTime: "date",
  MySqlYear: "number",
  MySqlEnum: "enum",
  MySqlJson: "json",
  MySqlSerial: "number",
  MySqlReal: "number",
  MySqlDouble: "number",
  MySqlFloat: "number",
  MySqlDecimal: "currency",
  MySqlTime: "string",
  MySqlBlob: "file",
  MySqlTinyBlob: "file",
  MySqlMediumBlob: "file",
  MySqlLongBlob: "file",
  MySqlTinyText: "text",
  MySqlMediumText: "text",
  MySqlLongText: "text",
  
  // SQLite types
  SQLiteText: "text",
  SQLiteInteger: "number",
  SQLiteReal: "number",
  SQLiteBoolean: "boolean",
  SQLiteTimestamp: "date",
  SQLiteBlob: "file",
  SQLiteNumeric: "number",
};

// Helper function to detect field type based on column name patterns
function detectColumnTypeByName(columnName: string, baseType: ColumnType): ColumnType {
  const name = columnName.toLowerCase();
  
  // Date/Time detection (PRIORITY: Handle first to override other detections)
  if (name === "createdat" || name === "created_at" || 
      name === "updatedat" || name === "updated_at" ||
      name === "deletedat" || name === "deleted_at" ||
      name.includes("time") || name.includes("date") ||
      name.endsWith("at") && (name.includes("created") || name.includes("updated") || name.includes("deleted"))) {
    return "date";
  }
  
  // Email detection (exact matches or clear email fields)
  if (name === "email" || name === "useremail" || name === "emailaddress" || name.endsWith("email")) {
    return "email";
  }
  
  // URL detection (exact matches or clear URL fields)
  if (name === "url" || name === "link" || name === "website" || name === "homepage" || name.endsWith("url") || name.endsWith("link")) {
    return "url";
  }
  
  // Phone detection (exact matches or clear phone fields)
  if (name === "phone" || name === "mobile" || name === "tel" || name === "telephone" || name.endsWith("phone") || name.endsWith("mobile")) {
    return "phone";
  }
  
  // Color detection (exact matches)
  if (name === "color" || name === "colour" || name.endsWith("color") || name.endsWith("colour")) {
    return "color";
  }
  
  // File size detection (for number fields)  
  if (baseType === "number" && (name === "filesize" || name === "file_size" || name === "size" || name.endsWith("size") || name.toLowerCase().includes("filesize"))) {
    return "filesize";
  }
  
  // Currency detection (for number fields) - be more specific
  if (baseType === "number" && (name.includes("price") || name.includes("amount") || name.includes("cost") || name.includes("fee") || name.includes("salary") || name === "currency")) {
    return "currency";
  }
  
  // File name detection (not file upload, just display)
  if (name === "filename" || name === "file_name" || name === "originalname" || name === "original_name") {
    return "file"; // Special handling for file names
  }
  
  // Image detection (only for actual image fields, not URLs)
  if ((name === "image" || name === "avatar" || name === "photo" || name === "picture") && baseType === "string" && !name.includes("url")) {
    return "image";
  }
  
  // Rich text detection (exact matches)
  if (name.includes("richtext") || name.includes("rich_text") || name.includes("html")) {
    return "richtext";
  }
  
  // Markdown detection (exact matches)
  if (name.includes("markdown") || name.includes("md")) {
    return "markdown";
  }
  
  // Tags detection (exact matches for tag fields)
  if (name === "tags" || name === "keywords" || name === "labels" || name.endsWith("tags")) {
    return "tags";
  }
  
  // Password detection (exact matches)
  if (name === "password" || name === "passwd" || name === "secret" || name.endsWith("password")) {
    return "password";
  }
  
  // Textarea detection for long text fields (more specific)
  if (baseType === "string" && (name.includes("description") || name.includes("content") || name.includes("note") || name.includes("comment") || name.includes("message") || name.includes("bio") || name.includes("summary"))) {
    return "textarea";
  }
  
  return baseType;
}

// Helper function to determine the best display field for a foreign table
export async function getDisplayFieldForTable(tableName: string): Promise<string> {
  // Table-specific display field preferences
  const tableDisplayFields: Record<string, string> = {
    'users': 'name',
    'user': 'name',
    'categories': 'name',
    'category': 'name',
    'products': 'name',
    'product': 'name',
    'orders': 'id', // For orders, ID might be more meaningful
    'order': 'id',
    'payments': 'id',
    'payment': 'id',
    'subscriptions': 'id',
    'subscription': 'id',
  };
  
  // Check if we have a specific preference for this table
  if (tableDisplayFields[tableName]) {
    return tableDisplayFields[tableName];
  }
  
  // Default fallback: use 'name' if available, otherwise 'id'
  return 'name';
}

export async function getTableSchema(
  _table: DbTable | any,
  tableName: EnabledTableKeys,
): Promise<SchemaInfo> {

  // load the table from schema
  const loadedTable = schema[tableName as keyof typeof schema];
  const tableDetails = TableConfig(loadedTable);
  const config = adminTableConfig[tableName] ?? {};
  const userRelatedColumn =
    typeof config.userRelated === "string"
      ? config.userRelated
      : config.userRelated
        ? "userId"
        : null;
  const userTableName = TableConfig(schema.users)?.name;

  return Promise.all(tableDetails.columns.map(async (column: DbColumn) => {
    let columnType: ColumnType =
      drizzleTypeToTsType[column.dataType] || "string";

    const foreignKey = tableDetails.foreignKeys.find((fk: any) =>
      fk.reference().columns.some((c: DbColumn) => c.name === column.name),
    );

    const foreignTableName = foreignKey
      ? TableConfig(foreignKey.reference().foreignTable).name
      : undefined;
    const isUserIdFk = foreignTableName === userTableName;

    let foreignKeyInfo: ColumnInfo['foreignKey'] = undefined;

    // Handle user relationship
    if (column.name === userRelatedColumn && isUserIdFk) {
      columnType = "user_id";
      foreignKeyInfo = {
        table: foreignTableName!,
        column: foreignKey!.reference().columns[0].name,
        displayField: "name", // Default to 'name' field for users
      };
    } 
    // Handle foreign key relationships (non-user)
    else if (foreignKey && !isUserIdFk) {
      columnType = "foreign_key";
      foreignKeyInfo = {
        table: foreignTableName!,
        column: foreignKey!.reference().columns[0].name,
        displayField: await getDisplayFieldForTable(foreignTableName!), // Determine display field
      };
    }
    // Apply intelligent type detection based on column name
    else {
      columnType = detectColumnTypeByName(column.name, columnType);
    }

    const isPrimaryKey = column.primary;
    const isOptional = !column.notNull || column.hasDefault || isPrimaryKey;

    return {
      name: column.name,
      type: columnType,
      isOptional,
      isPrimaryKey,
      isAutoGenerated: column.hasDefault && isPrimaryKey,
      enumValues:
        "enumValues" in column ? (column.enumValues as string[]) : undefined,
      foreignKey: foreignKeyInfo,
    };
  }));
}