# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/.swc
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
pnpm-lock.yaml
yarn.lock
package-lock.json

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
*.bak

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env files (can opt-in for committing if needed)
.env.local*
.env.development*
.env.test*
.env.production*
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# database
/database