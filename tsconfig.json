{
  "compilerOptions": {
    /* === Core === */
    "target": "ESNext",
    "module": "ESNext",
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ],
    "moduleResolution": "Bundler",
    "jsx": "preserve",
    "noEmit": true,
    "incremental": true,
    "isolatedModules": true,
    /* === Type safety === */
    "strict": true,
    "skipLibCheck": true,
    /* === Interop & Imports === */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    /* === Project structure === */
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@/env": [
        "env.ts"
      ],
      "@/keystatic.config": [
        "./keystatic.config.ts"
      ],
      "@/styles/*": [
        "./styles/*"
      ]
    },
    /* === Environment === */
    "types": [
      "node"
    ],
    /* === Next.js Plugin === */
    "plugins": [
      {
        "name": "next"
      }
    ],
    "allowJs": true
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}
