import env from "env";

// Import all schemas
import * as postgresSchema from "./postgres/schema";
import * as mysqlSchema from "./mysql/schema";
import * as sqliteSchema from "./sqlite/schema";

// Select the appropriate schema based on the database engine
const schema =
  env.DATABASE_ENGINE === "postgres" ? postgresSchema :
  env.DATABASE_ENGINE === "mysql" ? mysqlSchema :
  env.DATABASE_ENGINE === "sqlite" ? sqliteSchema :
  postgresSchema;

// Re-export all schema elements
export const {
  users,
  accounts,
  sessions,
  verifications,
  subscriptions,
  payments,
  webhookEvents,
  uploads,
  rateLimits,
  settings,
  userRoleEnum,
} = schema;
