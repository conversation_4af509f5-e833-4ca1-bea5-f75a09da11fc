export interface ProductFeature {
  name: string;
  included: boolean;
  description?: string;
}

export interface PricingTier {
  id: string;
  name: string;
  description: string;
  isPopular: boolean;
  features: ProductFeature[];
  pricing: {
    creem: {
      oneTime: string;
      monthly: string;
      yearly: string;
    };
  };
  prices: {
    oneTime: number;
    monthly: number;
    yearly: number;
  };
  currency: "USD" | "EUR";
}