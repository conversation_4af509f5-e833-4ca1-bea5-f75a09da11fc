import type { enabledTablesMap } from "@/lib/config/admin-tables";

export type EnabledTableKeys = keyof typeof enabledTablesMap;
export type EnabledTable = (typeof enabledTablesMap)[EnabledTableKeys];

export interface TableConfig {
  userRelated?: boolean | string;
  hiddenColumns?: string[];
  readOnlyColumns?: string[];
}

export type ColumnType =
  | "string"
  | "number"
  | "boolean"
  | "date"
  | "enum"
  | "json"
  | "uuid"
  | "text"
  | "user_id"
  | "email"
  | "url"
  | "phone"
  | "color"
  | "file"
  | "image"
  | "richtext"
  | "markdown"
  | "tags"
  | "currency"
  | "foreign_key"
  | "textarea"
  | "password"
  | "filesize";
export interface ColumnInfo {
  name: string;
  type: ColumnType;
  isOptional: boolean;
  isPrimaryKey: boolean;
  isAutoGenerated: boolean;
  enumValues?: string[];
  foreignKey?: {
    table: string;
    column: string;
    displayField?: string;
  };
}

export type SchemaInfo = ColumnInfo[];

export interface AdminStats {
  users: {
    total: number;
    verified: number;

    admins: number;
  };
  subscriptions: {
    total: number;
    active: number;

    canceled: number;
  };
  payments: {
    total: number;
    totalRevenue: number;
    successful: number;
  };
  uploads: {
    total: number;
    totalSize: number;
  };
}

export interface AdminStatsCardsProps {
  initialStats: AdminStats;
}

