{"version": "6", "dialect": "sqlite", "id": "7d9233fb-216c-4eeb-83f8-5238dab36799", "prevId": "********-0000-0000-0000-************", "tables": {"accounts": {"name": "accounts", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerId": {"name": "providerId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "idToken": {"name": "idToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "accessTokenExpiresAt": {"name": "accessTokenExpiresAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refreshTokenExpiresAt": {"name": "refreshTokenExpiresAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"accounts_userId_idx": {"name": "accounts_userId_idx", "columns": ["userId"], "isUnique": false}}, "foreignKeys": {"accounts_userId_users_id_fk": {"name": "accounts_userId_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payments": {"name": "payments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customerId": {"name": "customerId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscriptionId": {"name": "subscriptionId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "productId": {"name": "productId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "paymentId": {"name": "paymentId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'usd'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "paymentType": {"name": "paymentType", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"payments_paymentId_unique": {"name": "payments_paymentId_unique", "columns": ["paymentId"], "isUnique": true}, "payments_userId_idx": {"name": "payments_userId_idx", "columns": ["userId"], "isUnique": false}}, "foreignKeys": {"payments_userId_users_id_fk": {"name": "payments_userId_users_id_fk", "tableFrom": "payments", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "rate_limits": {"name": "rate_limits", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "lastRequest": {"name": "lastRequest", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"rate_limits_key_unique": {"name": "rate_limits_key_unique", "columns": ["key"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "expiresAt": {"name": "expiresAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "os": {"name": "os", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "browser": {"name": "browser", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "deviceType": {"name": "deviceType", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"sessions_token_unique": {"name": "sessions_token_unique", "columns": ["token"], "isUnique": true}}, "foreignKeys": {"sessions_userId_users_id_fk": {"name": "sessions_userId_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "settings": {"name": "settings", "columns": {"key": {"name": "key", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "subscriptions": {"name": "subscriptions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customerId": {"name": "customerId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscriptionId": {"name": "subscriptionId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "productId": {"name": "productId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "currentPeriodStart": {"name": "currentPeriodStart", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "currentPeriodEnd": {"name": "currentPeriodEnd", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "canceledAt": {"name": "canceledAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"subscriptions_subscriptionId_unique": {"name": "subscriptions_subscriptionId_unique", "columns": ["subscriptionId"], "isUnique": true}, "subscriptions_userId_idx": {"name": "subscriptions_userId_idx", "columns": ["userId"], "isUnique": false}, "subscriptions_customerId_idx": {"name": "subscriptions_customerId_idx", "columns": ["customerId"], "isUnique": false}}, "foreignKeys": {"subscriptions_userId_users_id_fk": {"name": "subscriptions_userId_users_id_fk", "tableFrom": "subscriptions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "uploads": {"name": "uploads", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileKey": {"name": "fileKey", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileName": {"name": "fileName", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileSize": {"name": "fileSize", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "contentType": {"name": "contentType", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"uploads_userId_idx": {"name": "uploads_userId_idx", "columns": ["userId"], "isUnique": false}, "uploads_fileKey_idx": {"name": "uploads_fileKey_idx", "columns": ["fileKey"], "isUnique": false}}, "foreignKeys": {"uploads_userId_users_id_fk": {"name": "uploads_userId_users_id_fk", "tableFrom": "uploads", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "emailVerified": {"name": "emailVerified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'user'"}, "paymentProviderCustomerId": {"name": "paymentProviderCustomerId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}, "users_paymentProviderCustomerId_unique": {"name": "users_paymentProviderCustomerId_unique", "columns": ["paymentProviderCustomerId"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verifications": {"name": "verifications", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expiresAt": {"name": "expiresAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "webhook_events": {"name": "webhook_events", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "eventId": {"name": "eventId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "eventType": {"name": "eventType", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'creem'"}, "processed": {"name": "processed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "processedAt": {"name": "processedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "payload": {"name": "payload", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"webhook_events_eventId_unique": {"name": "webhook_events_eventId_unique", "columns": ["eventId"], "isUnique": true}, "webhook_events_eventId_idx": {"name": "webhook_events_eventId_idx", "columns": ["eventId"], "isUnique": false}, "webhook_events_provider_idx": {"name": "webhook_events_provider_idx", "columns": ["provider"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}