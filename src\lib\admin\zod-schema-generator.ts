import z from "zod";
import { SchemaInfo } from "./types";

export async function generateZodSchema(
  schemaInfo: SchemaInfo,
  readOnlyColumns: string[] = [],
): Promise<z.ZodObject<any>> {
  const shape: Record<string, z.ZodType> = {};

  (await schemaInfo).forEach((col) => {
    if (col.isAutoGenerated || readOnlyColumns.includes(col.name)) return;

    let fieldSchema: z.ZodType;

    switch (col.type) {
      case "string":
      case "uuid":
      case "user_id":
      case "foreign_key":
        fieldSchema = z.string();
        if (!col.isOptional) {
          fieldSchema = (fieldSchema as z.ZodString).min(1, {
            message: "This field is required",
          });
        }
        break;
      case "email":
        fieldSchema = z.string().email("Please enter a valid email address");
        if (!col.isOptional) {
          fieldSchema = (fieldSchema as z.ZodString).min(1, {
            message: "This field is required",
          });
        }
        break;
      case "url":
        fieldSchema = z.string().url("Please enter a valid URL");
        if (!col.isOptional) {
          fieldSchema = (fieldSchema as z.ZodString).min(1, {
            message: "This field is required",
          });
        }
        break;
      case "phone":
        fieldSchema = z.string().regex(/^[+]?[\d\s\-()]+$/, "Please enter a valid phone number");
        if (!col.isOptional) {
          fieldSchema = (fieldSchema as z.ZodString).min(1, {
            message: "This field is required",
          });
        }
        break;
      case "color":
        fieldSchema = z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Please enter a valid color (e.g., #FF0000)");
        if (!col.isOptional) {
          fieldSchema = (fieldSchema as z.ZodString).min(1, {
            message: "This field is required",
          });
        }
        break;
      case "text":
      case "textarea":
      case "richtext":
      case "markdown":
      case "file":
      case "image":
      case "password":
        fieldSchema = z.string();
        if (!col.isOptional) {
          fieldSchema = (fieldSchema as z.ZodString).min(1, {
            message: "This field is required",
          });
        }
        break;
      case "tags":
        fieldSchema = z.string().transform((str) => {
          if (!str) return [];
          return str.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
        });
        break;
      case "number":
        fieldSchema = z.coerce.number();
        break;
      case "currency":
        fieldSchema = z.coerce.number().min(0, "Amount must be positive");
        break;
      case "filesize":
        fieldSchema = z.coerce.number().min(0, "File size must be positive");
        break;
      case "boolean":
        fieldSchema = z.boolean().default(false);
        break;
      case "date":
        fieldSchema = z.preprocess((arg) => {
          if (typeof arg == "string" && arg) return new Date(arg);
          if (!arg) return undefined;
          return arg;
        }, z.date().optional());
        break;
      case "enum":
        if (col.enumValues && col.enumValues.length > 0) {
          fieldSchema = z.enum(col.enumValues as [string, ...string[]]);
        } else {
          fieldSchema = z.string();
        }
        break;
      case "json":
        fieldSchema = z
          .string()
          .transform((str, ctx) => {
            if (!str) return null;
            try {
              return JSON.parse(str);
            } catch {
              ctx.addIssue({ code: "custom", message: "Invalid JSON" });
              return z.NEVER;
            }
          })
          .pipe(z.any());
        break;
      default:
        // This should not happen with the current types, but as a fallback:
        fieldSchema = z.any();
    }

    if (col.isOptional) {
      shape[col.name] = fieldSchema.optional().nullable();
    } else {
      shape[col.name] = fieldSchema;
    }
  });

  return z.object(shape);
}