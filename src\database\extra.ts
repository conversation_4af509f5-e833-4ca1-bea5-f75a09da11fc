import env from "env";

// Import based on database engine
let AnyColumn: any;
let DbTable: any;
let TableConfig: any;

if (env.DATABASE_ENGINE === "postgres") {
  const pg = await import("drizzle-orm/pg-core");
  AnyColumn = pg.PgColumn;
  DbTable = pg.PgTable;
  TableConfig = pg.getTableConfig;
} else if (env.DATABASE_ENGINE === "mysql") {
  const mysql = await import("drizzle-orm/mysql-core");
  AnyColumn = mysql.MySqlColumn;
  DbTable = mysql.MySqlTable;
  TableConfig = mysql.getTableConfig;
} else if (env.DATABASE_ENGINE === "sqlite") {
  const sqlite = await import("drizzle-orm/sqlite-core");
  AnyColumn = sqlite.SQLiteColumn;
  DbTable = sqlite.SQLiteTable;
  TableConfig = sqlite.getTableConfig;
} else {
  // Default to postgres
  const pg = await import("drizzle-orm/pg-core");
  AnyColumn = pg.PgColumn;
  DbTable = pg.PgTable;
  TableConfig = pg.getTableConfig;
}

export { AnyColumn, DbTable, TableConfig };