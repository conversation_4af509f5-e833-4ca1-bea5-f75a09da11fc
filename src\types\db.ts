import type { PgColumn, PgTable, PgTransaction } from "drizzle-orm/pg-core";
import type { MySqlColumn, MySqlTable, MySqlTransaction } from "drizzle-orm/mysql-core";
import type { SQLiteColumn, SQLiteTable, SQLiteTransaction } from "drizzle-orm/sqlite-core";

export type DbEngine = "postgres" | "mysql" | "sqlite";

export type DbTransaction = 
  | PgTransaction<any>
  | MySqlTransaction<any, any, any>
  | SQLiteTransaction<any, any, any, any>;

export type DbColumn =
  | PgColumn
  | MySqlColumn
  | SQLiteColumn;

export type DbTable =
  | PgTable
  | MySqlTable
  | SQLiteTable;

