"use server";

import { db, returning } from "@/database";
import {
  enabledTablesMap,
} from "@/lib/config/admin-tables";
import { type EnabledTableKeys } from "@/types/admin";
import { requireAdmin } from "@/lib/auth/permissions";
import { createSafeActionClient } from "next-safe-action";
import { count, eq, like, inArray, or, SQL, and } from "drizzle-orm";
import { z } from "zod";
import { revalidatePath } from "next/cache";
import { TableConfig } from "@/database/utils";
import * as schema from "@/database/schema";
import { getTableSchema, getDisplayFieldForTable } from "@/lib/admin/schema-generator";
import type { DbTable, DbColumn } from "@/types/db";
import { type ColumnInfo } from "@/types/admin";

interface RecordWithId {
  id: string | number;
  [key: string]: unknown;
}

const action = createSafeActionClient();

const adminAction = action.use(async ({ next }) => {
  const user = await requireAdmin();
  return next({
    ctx: {
      user,
    },
  });
});

async function enhanceRecordsWithForeignKeys(
  records: RecordWithId[],
  tableName: EnabledTableKeys,
): Promise<RecordWithId[]> {
  if (records.length === 0) return records;
  
  const table = enabledTablesMap[tableName];
  const schemaInfo = await getTableSchema(table, tableName);

  const foreignKeyColumns = schemaInfo.filter((col: ColumnInfo) => col.foreignKey);
  
  if (foreignKeyColumns.length === 0) return records;
  
  const foreignKeysByTable = new Map<string, typeof foreignKeyColumns>();
  for (const col of foreignKeyColumns) {
    if (col.foreignKey) {
      const targetTable = col.foreignKey.table;
      if (!foreignKeysByTable.has(targetTable)) {
        foreignKeysByTable.set(targetTable, []);
      }
      foreignKeysByTable.get(targetTable)!.push(col);
    }
  }
  
  const enhancedRecords = [...records];
  
  for (const [targetTableName, columns] of foreignKeysByTable) {
    if (!(targetTableName in enabledTablesMap)) continue;
    
    const targetTable: any = enabledTablesMap[targetTableName as EnabledTableKeys];
    
    const targetIds = new Set<string>();
    for (const record of records) {
      for (const col of columns) {
        const id = record[col.name];
        if (id && typeof id === 'string') {
          targetIds.add(id);
        }
      }
    }
    
    if (targetIds.size === 0) continue;
    
    try {
      const displayField = await getDisplayFieldForTable(targetTableName);

      const selectFields: Record<string, DbColumn> = { id: (targetTable as DbTable & { id: DbColumn }).id };

      const tableConfig = TableConfig(targetTable as DbTable);
      const tableColumns = tableConfig.columns;

      const possibleDisplayFields = [displayField, 'name', 'title', 'email', 'username'];
      for (const fieldName of possibleDisplayFields) {
        const column = tableColumns.find((c: DbColumn) => c.name === fieldName);
        if (column) {
          selectFields[fieldName] = column;
        }
      }
      
      const referencedData = await db
        .select(selectFields)
        .from(targetTable)
        .where(inArray((targetTable as DbTable & { id: DbColumn }).id, Array.from(targetIds)));
      
      const dataMap = new Map(referencedData.map((item: Record<string, unknown>) => [item.id, item]));
      
      for (let i = 0; i < enhancedRecords.length; i++) {
        for (const col of columns) {
          const foreignId = enhancedRecords[i][col.name];
          if (foreignId && typeof foreignId === 'string') {
            const referencedItem = dataMap.get(foreignId);
            if (referencedItem) {
              enhancedRecords[i][`${col.name}_ref`] = {
                ...referencedItem,
                _tableName: targetTableName,
                _displayField: displayField,
              };
            }
          }
        }
      }
    } catch (error) {
      console.warn(`Failed to fetch data for table ${targetTableName}:`, error);
    }
  }
  
  return enhancedRecords;
}

export async function getGenericTableData(
  tableName: EnabledTableKeys,
  options: {
    page: number;
    limit: number;
    search?: string;
    sort?: { by: string; order: "asc" | "desc" };
  },
): Promise<{
  data: RecordWithId[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> {
  const table = enabledTablesMap[tableName];
  const { page, limit, search } = options;
  const offset = (page - 1) * limit;

  const loadedTable = schema[table as keyof typeof schema];
  const tableColumns = TableConfig(loadedTable).columns;
  const textColumns = tableColumns.filter(
    (c: any): c is DbColumn =>
      c.getSQLType() === "text" || c.getSQLType().includes("varchar"),
  );

  const whereConditions: SQL[] = [];
  if (search && textColumns.length > 0) {
    whereConditions.push(
      or(...textColumns.map((col: any) => like(col, `%${search}%`)))!,
    );
  }

  const finalWhere =
    whereConditions.length > 0 ? and(...whereConditions) : undefined;

  const dataQuery = db
    .select()
    .from(loadedTable)
    .where(finalWhere)
    .limit(limit)
    .offset(offset);
  const totalQuery = db
    .select({ total: count() })
    .from(loadedTable)
    .where(finalWhere);

  const [data, [{ total }]] = await Promise.all([dataQuery, totalQuery]);

  const enhancedData = await enhanceRecordsWithForeignKeys(data as RecordWithId[], tableName);

  return {
    data: enhancedData,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };
}

const tableNameSchema = z.custom<EnabledTableKeys>((val) =>
  Object.keys(enabledTablesMap).includes(val as string),
);

export const createRecord = adminAction
  .inputSchema(
    z.object({
      tableName: tableNameSchema,
      data: z.record(z.unknown()),
    }),
  )
  .action(async ({ parsedInput: { tableName, data } }: any) => {
    const table = enabledTablesMap[tableName];
    const loadedTable = schema[table as keyof typeof schema];
    const cleanData = Object.fromEntries(
      Object.entries(data).filter(([, value]) => value !== undefined),
    );

    const [newRecord] = await returning(
      db.insert(loadedTable)
      .values(cleanData as never)
    );
    revalidatePath(`/dashboard/admin/tables/${tableName}`);
    return { record: newRecord };
  });

export const updateRecord = adminAction
  .inputSchema(
    z.object({
      tableName: tableNameSchema,
      id: z.union([z.string(), z.number()]),
      data: z.record(z.unknown()),
    }),
  )
  .action(async ({ parsedInput: { tableName, id, data } }: any) => {
    const table = enabledTablesMap[tableName];
    const loadedTable = schema[table as keyof typeof schema];
    const cleanData = Object.fromEntries(
      Object.entries(data).filter(([, value]) => value !== undefined),
    );

    type UpdateType = Partial<typeof loadedTable.$inferSelect>;
    const [updatedRecord] = await returning(
      db.update(loadedTable)
      .set(cleanData as UpdateType)
      .where(eq(loadedTable.id, id))
    );

    revalidatePath(`/dashboard/admin/tables/${tableName}`);
    return { record: updatedRecord };
  });

export const deleteRecords = adminAction
  .inputSchema(
    z.object({
      tableName: tableNameSchema,
      ids: z.array(z.union([z.string(), z.number()])).min(1),
    }),
  )
  .action(async ({ parsedInput: { tableName, ids } }: any) => {
    const table = enabledTablesMap[tableName];
    const loadedTable = schema[table as keyof typeof schema];
    const [result] = await returning(
      db.delete(loadedTable)
      .where(inArray(loadedTable.id, ids))
    );

    revalidatePath(`/dashboard/admin/tables/${tableName}`);
    return { success: true, count: result.length };
  });
