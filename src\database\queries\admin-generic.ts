"use server";

import { db, returning } from "@/database";
import {
  enabledTablesMap,
} from "@/lib/config/admin-tables";
import { type EnabledTableKeys } from "@/types/admin";
import { requireAdmin } from "@/lib/auth/permissions";
import { createSafeActionClient } from "next-safe-action";
import { count, eq, ilike, inArray, or, SQL, and } from "drizzle-orm";
import { z } from "zod";
import { revalidatePath } from "next/cache";
import { AnyColumn, DbTable, TableConfig } from "@/database/extra";
import * as schema from "@/database/schema";
import { getTableSchema, getDisplayFieldForTable } from "@/lib/admin/schema-generator";
import { type ColumnInfo } from "@/lib/admin/types";

interface RecordWithId {
  id: string | number;
  [key: string]: unknown;
}

const action = createSafeActionClient();

const adminAction = action.use(async ({ next }) => {
  const user = await requireAdmin();
  return next({
    ctx: {
      user,
    },
  });
});

async function enhanceRecordsWithForeignKeys(
  records: RecordWithId[],
  tableName: EnabledTableKeys,
): Promise<RecordWithId[]> {
  if (records.length === 0) return records;
  
  const table = enabledTablesMap[tableName];
  const schemaInfo = await getTableSchema(table, tableName);

  const foreignKeyColumns = schemaInfo.filter((col: ColumnInfo) => col.foreignKey);
  
  if (foreignKeyColumns.length === 0) return records;
  
  const foreignKeysByTable = new Map<string, typeof foreignKeyColumns>();
  for (const col of foreignKeyColumns) {
    if (col.foreignKey) {
      const targetTable = col.foreignKey.table;
      if (!foreignKeysByTable.has(targetTable)) {
        foreignKeysByTable.set(targetTable, []);
      }
      foreignKeysByTable.get(targetTable)!.push(col);
    }
  }
  
  const enhancedRecords = [...records];
  
  for (const [targetTableName, columns] of foreignKeysByTable) {
    if (!(targetTableName in enabledTablesMap)) continue;
    
    const targetTable = enabledTablesMap[targetTableName as EnabledTableKeys];
    
    const targetIds = new Set<string>();
    for (const record of records) {
      for (const col of columns) {
        const id = record[col.name];
        if (id && typeof id === 'string') {
          targetIds.add(id);
        }
      }
    }
    
    if (targetIds.size === 0) continue;
    
    try {
      const displayField = await getDisplayFieldForTable(targetTableName);

      const selectFields: Record<string, typeof AnyColumn> = { id: (targetTable as typeof DbTable & { id: typeof AnyColumn }).id };

      const tableConfig = TableConfig(targetTable as typeof DbTable);
      const tableColumns = tableConfig.columns;

      const possibleDisplayFields = [displayField, 'name', 'title', 'email', 'username'];
      for (const fieldName of possibleDisplayFields) {
        const column = tableColumns.find((c: typeof AnyColumn) => c.name === fieldName);
        if (column) {
          selectFields[fieldName] = column;
        }
      }
      
      const referencedData = await db
        .select(selectFields)
        .from(targetTable)
        .where(inArray((targetTable as typeof DbTable & { id: typeof AnyColumn }).id, Array.from(targetIds)));
      
      const dataMap = new Map(referencedData.map((item: Record<string, unknown>) => [item.id, item]));
      
      for (let i = 0; i < enhancedRecords.length; i++) {
        for (const col of columns) {
          const foreignId = enhancedRecords[i][col.name];
          if (foreignId && typeof foreignId === 'string') {
            const referencedItem = dataMap.get(foreignId);
            if (referencedItem) {
              enhancedRecords[i][`${col.name}_ref`] = {
                ...referencedItem,
                _tableName: targetTableName,
                _displayField: displayField,
              };
            }
          }
        }
      }
    } catch (error) {
      console.warn(`Failed to fetch data for table ${targetTableName}:`, error);
    }
  }
  
  return enhancedRecords;
}

export async function getGenericTableData(
  tableName: EnabledTableKeys,
  options: {
    page: number;
    limit: number;
    search?: string;
    sort?: { by: string; order: "asc" | "desc" };
  },
): Promise<{
  data: RecordWithId[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> {
  const table = enabledTablesMap[tableName];
  const { page, limit, search } = options;
  const offset = (page - 1) * limit;

  const loadedTable = schema[table as keyof typeof schema];
  const tableColumns = TableConfig(loadedTable).columns;
  const textColumns = tableColumns.filter(
    (c: any): c is typeof AnyColumn =>
      c.getSQLType() === "text" || c.getSQLType().includes("varchar"),
  );

  const whereConditions: SQL[] = [];
  if (search && textColumns.length > 0) {
    whereConditions.push(
      or(...textColumns.map((col: any) => ilike(col, `%${search}%`)))!,
    );
  }

  const finalWhere =
    whereConditions.length > 0 ? and(...whereConditions) : undefined;

  const dataQuery = db
    .select()
    .from(loadedTable)
    .where(finalWhere)
    .limit(limit)
    .offset(offset);
  const totalQuery = db
    .select({ total: count() })
    .from(loadedTable)
    .where(finalWhere);

  const [data, [{ total }]] = await Promise.all([dataQuery, totalQuery]);

  const enhancedData = await enhanceRecordsWithForeignKeys(data as RecordWithId[], tableName);

  return {
    data: enhancedData,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };
}

const tableNameSchema = z.custom<EnabledTableKeys>((val) =>
  Object.keys(enabledTablesMap).includes(val as string),
);

export const createRecord = adminAction
  .schema(
    z.object({
      tableName: tableNameSchema,
      data: z.record(z.unknown()),
    }),
  )
  .action(async ({ parsedInput: { tableName, data } }) => {
    const table = enabledTablesMap[tableName];
    const cleanData = Object.fromEntries(
      Object.entries(data).filter(([, value]) => value !== undefined),
    );

    const [newRecord] = await returning(
      db.insert(table)
      .values(cleanData as never)
    );
    revalidatePath(`/dashboard/admin/tables/${tableName}`);
    return { record: newRecord };
  });

export const updateRecord = adminAction
  .schema(
    z.object({
      tableName: tableNameSchema,
      id: z.union([z.string(), z.number()]),
      data: z.record(z.unknown()),
    }),
  )
  .action(async ({ parsedInput: { tableName, id, data } }) => {
    const table = enabledTablesMap[tableName] as typeof DbTable & { id: typeof AnyColumn };
    const cleanData = Object.fromEntries(
      Object.entries(data).filter(([, value]) => value !== undefined),
    );

    type UpdateType = Partial<typeof table.$inferSelect>;
    const [updatedRecord] = await returning(
      db.update(table)
      .set(cleanData as UpdateType)
      .where(eq(table.id, id))
    );

    revalidatePath(`/dashboard/admin/tables/${tableName}`);
    return { record: updatedRecord };
  });

export const deleteRecords = adminAction
  .schema(
    z.object({
      tableName: tableNameSchema,
      ids: z.array(z.union([z.string(), z.number()])).min(1),
    }),
  )
  .action(async ({ parsedInput: { tableName, ids } }) => {
    const table = enabledTablesMap[tableName] as typeof DbTable & { id: typeof AnyColumn };
    const [result] = await returning(
      db.delete(table)
      .where(inArray(table.id, ids))
    );

    revalidatePath(`/dashboard/admin/tables/${tableName}`);
    return { success: true, count: result.length };
  });
