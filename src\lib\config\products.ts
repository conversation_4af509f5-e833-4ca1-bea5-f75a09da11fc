import { PricingTier } from "@/types/products";

export const PRODUCT_TIERS: PricingTier[] = [
  {
    id: "plus",
    name: "Plus",
    description: "Best for growing teams and businesses",
    isPopular: false,
    features: [
      { name: "Unlimited projects", included: true },
      { name: "Advanced analytics", included: true },
      { name: "Priority support", included: true },
      { name: "10GB storage", included: false },
      { name: "Team collaboration", included: false },
      { name: "API access", included: false },
      { name: "Dedicated support", included: false },
      { name: "Advanced security", included: false },
    ],
    pricing: {
      creem: {
        oneTime: "prod_2OGh6sLhXb9j5cikyxo8MJ",
        monthly: "prod_2OGh6sLhXb9j5cikyxo8MJ",
        yearly: "prod_2OGh6sLhXb9j5cikyxo8MJ",
      },
    },
    prices: {
      oneTime: 19.99,
      monthly: 9.99,
      yearly: 99.99,
    },
    currency: "USD",
  },
  {
    id: "pro",
    name: "Professional",
    description: "Best for growing teams and businesses",
    isPopular: true,
    features: [
      { name: "Unlimited projects", included: true },
      { name: "Advanced analytics", included: true },
      { name: "Priority support", included: true },
      { name: "10GB storage", included: true },
      { name: "Team collaboration", included: true },
      { name: "API access", included: true },
      { name: "Dedicated support", included: false },
      { name: "Advanced security", included: false },
    ],
    pricing: {
      creem: {
        oneTime: "prod_2OGh6sLhXb9j5cikyxo8MJ",
        monthly: "prod_2OGh6sLhXb9j5cikyxo8MJ",
        yearly: "prod_2OGh6sLhXb9j5cikyxo8MJ",
      },
    },
    prices: {
      oneTime: 29.99,
      monthly: 19.99,
      yearly: 199.99,
    },
    currency: "USD",
  },
  {
    id: "team",
    name: "Team",
    description: "Best for growing teams and businesses",
    isPopular: false,
    features: [
      { name: "Unlimited projects", included: true },
      { name: "Advanced analytics", included: true },
      { name: "Priority support", included: true },
      { name: "10GB storage", included: true },
      { name: "Team collaboration", included: true },
      { name: "API access", included: true },
      { name: "Dedicated support", included: true },
      { name: "Advanced security", included: true },
    ],
    pricing: {
      creem: {
        oneTime: "prod_2OGh6sLhXb9j5cikyxo8MJ",
        monthly: "prod_2OGh6sLhXb9j5cikyxo8MJ",
        yearly: "prod_2OGh6sLhXb9j5cikyxo8MJ",
      },
    },
    prices: {
      oneTime: 59.99,
      monthly: 49.99,
      yearly: 499.99,
    },
    currency: "USD",
  },
];

export const getProductTierById = (id: string): PricingTier | undefined => {
  return PRODUCT_TIERS.find((tier) => tier.id === id);
};

export const getProductTierByProductId = (
  productId: string,
): PricingTier | undefined => {
  for (const tier of PRODUCT_TIERS) {
    if (Object.values(tier.pricing.creem).includes(productId)) {
      return tier;
    }
  }
  return undefined;
};
