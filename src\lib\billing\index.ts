import { PAYMENT_PROVIDER } from "@/lib/config/constants";
import type { PaymentProvider } from "@/types/billing";
import creemProvider from "./creem/provider";

let billingProvider: PaymentProvider;

switch (PAYMENT_PROVIDER) {
  case "creem":
    billingProvider = creemProvider;
    break;
  default:
    throw new Error(`Unsupported payment provider: ${PAYMENT_PROVIDER}`);
}

export const billing = billingProvider;
