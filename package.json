{"name": "zen-starter", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate --config=src/database/config.ts", "db:generate:prod": "drizzle-kit generate --config=src/database/config.prod.ts", "db:migrate:dev": "drizzle-kit migrate --config=src/database/config.ts", "db:migrate:prod": "drizzle-kit migrate --config=src/database/config.prod.ts", "db:push": "drizzle-kit push --config=src/database/config.ts", "prettier:check": "prettier --check --ignore-path .gitignore .", "prettier:format": "prettier --write --ignore-path .gitignore .", "analyze": "ANALYZE=true next build", "analyze:dev": "ANALYZE=true next dev", "set:admin": "tsx --env-file=.env scripts/set-admin.ts", "set:admin:prod": "tsx scripts/set-admin.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/lib-storage": "^3.832.0", "@aws-sdk/s3-request-presigner": "^3.832.0", "@hookform/resolvers": "^5.1.1", "@keystatic/core": "^0.5.47", "@keystatic/next": "^5.0.4", "@markdoc/markdoc": "^0.5.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.1.0", "@react-email/render": "^1.1.3", "@t3-oss/env-nextjs": "^0.13.8", "@vercel/edge-config": "^1.4.0", "better-auth": "^1.2.9", "better-sqlite3": "^12.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "creem": "^0.3.37", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.2", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.18.1", "input-otp": "^1.4.2", "lucide-react": "^0.518.0", "mailchecker": "^6.0.17", "mysql2": "^3.15.1", "next": "^15.3.4", "next-safe-action": "^8.0.3", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "nuqs": "^2.4.3", "postgres": "^3.4.7", "react": "^19.2.0", "react-day-picker": "^9.7.0", "react-dom": "^19.2.0", "react-hook-form": "^7.58.1", "react-qr-code": "^2.0.16", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.3", "resend": "^4.6.0", "sonner": "^2.0.5", "standardwebhooks": "^1.0.0", "tailwind-merge": "^3.3.1", "tsx": "^4.20.3", "use-debounce": "^10.0.5", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.4", "@tailwindcss/postcss": "^4.1.10", "@types/better-sqlite3": "^7.6.13", "@types/node": "^24.0.3", "@types/react": "^19.2.0", "@types/react-dom": "^19.2.0", "drizzle-kit": "^0.31.1", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "eslint-config-prettier": "^10.1.5", "postcss": "^8.5.6", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "react-email": "^4.0.16", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild", "sharp", "unrs-resolver"], "ignoredBuiltDependencies": ["better-sqlite3"]}}