# Database URL
DATABASE_ENGINE="postgres" # postgres | mysql | sqlite
DATABASE_URL="postgres://postgres:postgres@127.0.0.1:5432/zen_starter"

# DATABASE_ENGINE="mysql" # postgres | mysql | sqlite
# DATABASE_URL="mysql://root@127.0.0.1:3306/zen_starter"

# Database credentials (alternative to DATABASE_URL for postgres and mysql)
# DATABASE_ENGINE="mysql" # postgres | mysql 
# DATABASE_HOST="127.0.0.1"
# DATABASE_PORT="3306"
# DATABASE_NAME="zen_starter"
# DATABASE_USER="root"
# DATABASE_PASSWORD=""

# sqlite settings
# DATABASE_ENGINE="sqlite" 
# DATABASE_FILE="database/db.sqlite"

# Database SSL (for production)
# DATABASE_SSL=false        # true | false
# DATABASE_SSL_REJECT_UNAUTHORIZED=false # true | false
# DATABASE_SSL_CA="-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----"
# DATABASE_SSL_KEY="-----BEGIN PRIVATE KEY-----\

# Database connection pool settings for traditional servers
# DATABASE_POOL_SIZE=20
# DATABASE_IDLE_TIMEOUT=300
# DATABASE_MAX_LIFETIME=14400
# DATABASE_CONNECT_TIMEOUT=30

# Dynamic site settings
SETTINGS_BACKEND=file         # cloudflare kv | vercel | db | file
SETTINGS_DATABASE_BACKUP=true # optional DB backup for other backends

# Application settings
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Authentication credentials
GOOGLE_CLIENT_ID="optional"
GOOGLE_CLIENT_SECRET="optional"

GITHUB_CLIENT_ID="optional"
GITHUB_CLIENT_SECRET="optional"

LINKEDIN_CLIENT_ID="optional"
LINKEDIN_CLIENT_SECRET="optional"

# Better Auth Secret
BETTER_AUTH_SECRET="required"

# API keys
RESEND_API_KEY="required"

# Cloudflare R2 Storage
R2_ENDPOINT="https://YOUR-ID.r2.cloudflarestorage.com"
R2_ACCESS_KEY_ID="your-r2-access-key-id"
R2_SECRET_ACCESS_KEY="your-r2-secret-access-key"
R2_BUCKET_NAME="your-r2-bucket-name"
R2_PUBLIC_URL="http://localhost:3000"

# Payments
# test_mode / live_mode
CREEM_ENVIRONMENT=test_mode
CREEM_API_KEY=creem_test_6ucBs2fxev5HyLPiDfNXjI
CREEM_WEBHOOK_SECRET=whsec_test_secret_here