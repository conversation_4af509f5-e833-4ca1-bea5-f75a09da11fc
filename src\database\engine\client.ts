import env from "env";
import * as tables from "../tables";
import {
  getConnectionConfig,
  validateDatabaseConfig,
  buildDatabaseUrl,
} from "@/database/engine/config";

import type { DBEngine } from "@/types/db";
const engine = (env.DATABASE_ENGINE || "postgres") as DBEngine;
const connectionConfig = getConnectionConfig();
const databaseUrl = buildDatabaseUrl();

// In dev, ensure env & config are correct
if (process.env.NODE_ENV === "development") {
  validateDatabaseConfig();
}

let db: any;
let sql: any;
let provider: "mysql" | "sqlite" | "pg";
let closeDatabase: () => Promise<void>;

// Dynamic engine setup
switch (engine) {
  // -------------------- POSTGRES --------------------
  case "postgres": {
    const [{ drizzle }, postgres] = await Promise.all([
      import("drizzle-orm/postgres-js"),
      import("postgres"),
    ]);

    // postgres import interop
    const pg = (postgres as any).default ?? postgres;

    sql = pg(databaseUrl, connectionConfig);
    db = drizzle(sql, { schema: { ...tables } });
    provider = "pg";

    closeDatabase = async () => {
      await sql.end({ timeout: 5 });
    };

    break;
  }

  // -------------------- MYSQL --------------------
  case "mysql": {
    const [{ drizzle }, mysql] = await Promise.all([
      import("drizzle-orm/mysql2"),
      import("mysql2/promise"),
    ]);

    const mysqlModule: any = mysql;

    // Prefer a pool for mysql. Support passing a connection URI or credentials.
    if (typeof databaseUrl === "string" && databaseUrl.length > 0) {
      try {
        sql = mysqlModule.createPool({
          uri: databaseUrl,
          ...connectionConfig,
        });
      } catch (e) {
        // fallback to using just the connection config if URL parsing fails
        sql = mysqlModule.createPool(connectionConfig);
      }
    } else {
      // Use credentials-based connection
      sql = mysqlModule.createPool(connectionConfig);
    }

    db = drizzle(sql, { schema: { ...tables }, mode: "default" });

    provider = "mysql";

    closeDatabase = async () => {
      await sql.end();
    };

    break;
  }

  // -------------------- SQLITE --------------------
  case "sqlite": {
    const [{ drizzle }, sqlite3] = await Promise.all([
      import("drizzle-orm/better-sqlite3"),
      import("better-sqlite3"),
    ]);

    const sqlitePath = env.DATABASE_FILE || databaseUrl?.replace("file:", "") || ":memory:";

    // better-sqlite3 import interop
    const SqliteLib: any = (sqlite3 as any).default ?? sqlite3;

    // connectionConfig isn't directly passed to better-sqlite3; pass only accepted options
    const sqliteOpts: any = {};
    sql = new SqliteLib(sqlitePath, sqliteOpts);
    db = drizzle(sql, { schema: { ...tables } });
    provider = "sqlite";

    closeDatabase = async () => {
      sql.close();
    };

    break;
  }

  default:
    throw new Error(`Unsupported DB engine: ${engine}`);
}

async function returning(query: any, columns?: any) {
  const dbEngine = env.DATABASE_ENGINE?.toLowerCase();

  if (dbEngine === "mysql") {
    // MySQL doesn't support full RETURNING, using $returningId() instead
    return await query.$returningId();
  }

  // PostgreSQL and SQLite use .returning()
  return columns ? await query.returning(columns) : await query.returning();
}

export { db, sql, closeDatabase, provider, returning };
