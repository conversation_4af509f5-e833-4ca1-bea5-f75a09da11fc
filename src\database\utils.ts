import env from "env";

// Import based on database engine
let DbColumn: any;
let DbTable: any;
let TableConfig: any;
let DbTransaction: any;

if (env.DATABASE_ENGINE === "postgres") {
  const pg = await import("drizzle-orm/pg-core");
  DbColumn = pg.PgColumn;
  DbTable = pg.PgTable;
  TableConfig = pg.getTableConfig;
  DbTransaction = pg.PgTransaction;
} else if (env.DATABASE_ENGINE === "mysql") {
  const mysql = await import("drizzle-orm/mysql-core");
  DbColumn = mysql.MySqlColumn;
  DbTable = mysql.MySqlTable;
  TableConfig = mysql.getTableConfig;
  DbTransaction = mysql.MySqlTransaction;
} else if (env.DATABASE_ENGINE === "sqlite") {
  const sqlite = await import("drizzle-orm/sqlite-core");
  DbColumn = sqlite.SQLiteColumn;
  DbTable = sqlite.SQLiteTable;
  TableConfig = sqlite.getTableConfig;
  DbTransaction = sqlite.SQLiteTransaction;
}

export { DbColumn, DbTable, TableConfig, DbTransaction };