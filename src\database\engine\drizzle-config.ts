import { DbEngine } from "@/types/db";
import env from "env";

let dbDialect: "postgresql" | "mysql" | "sqlite";
let dbCredentials: any = {};
let dbEngine: DbEngine;

switch (env.DATABASE_ENGINE) {
  case "postgres":
    dbEngine = "postgres";
    dbDialect = "postgresql";

    if (env.DATABASE_URL) {
      dbCredentials = { url: env.DATABASE_URL };
    } else {
      dbCredentials = {
        host: env.DATABASE_HOST || "127.0.0.1",
        port: Number(env.DATABASE_PORT) || 5432,
        user: env.DATABASE_USER || "postgres",
        database: env.DATABASE_NAME,
      };

      if (env.DATABASE_PASSWORD != "") {
        dbCredentials.password = env.DATABASE_PASSWORD
      }

      if (env.DATABASE_SSL == true) {
        dbCredentials.ssl = {
          rejectUnauthorized: env.DATABASE_SSL_REJECT_UNAUTHORIZED === true,
          ca: env.DATABASE_SSL_CA || undefined,
          key: env.DATABASE_SSL_KEY || undefined,
        };
      }
    }
    break;

  case "mysql":
    dbDialect = "mysql";
    dbEngine = "mysql";
    if (env.DATABASE_URL) {
      dbCredentials = { url: env.DATABASE_URL };
    } else {
      dbCredentials = {
        host: env.DATABASE_HOST || "127.0.0.1",
        port: Number(env.DATABASE_PORT) || 3306,
        user: env.DATABASE_USER || "root",
        database: env.DATABASE_NAME,
      };
      
      if (env.DATABASE_PASSWORD != "") {
        dbCredentials.password = env.DATABASE_PASSWORD
      }

      if (env.DATABASE_SSL == true) {
        dbCredentials.ssl = {
          rejectUnauthorized: env.DATABASE_SSL_REJECT_UNAUTHORIZED === true,
          ca: env.DATABASE_SSL_CA || undefined,
          key: env.DATABASE_SSL_KEY || undefined,
        };
      }
    }
    break;

  case "sqlite":
    dbDialect = "sqlite";
    dbEngine = "sqlite";
    dbCredentials = {
      url:
        env.DATABASE_URL ||
        env.DATABASE_FILE ||
        "./database/dev-db.sqlite",
    };
    break;

  default:
    throw new Error(
      `Unsupported DATABASE_ENGINE: ${env.DATABASE_ENGINE}. Valid values are 'postgres', 'mysql', or 'sqlite'.`
    );
}

export { dbDialect, dbCredentials, dbEngine };